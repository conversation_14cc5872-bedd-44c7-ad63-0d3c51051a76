import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:universal_html/html.dart' as html;

class HttpService {
  static Future<Map<String, dynamic>> makeUniversalHttpRequest({
    required String url,
    required String method,
    Map<String, String>? headers,
    dynamic body,
  }) async {
    final completer = Completer<Map<String, dynamic>>();

    try {
      final request = html.HttpRequest();

      // Configure the request
      request.open(method, url);

      // Set headers
      if (headers != null) {
        headers.forEach((key, value) {
          try {
            request.setRequestHeader(key, value);
          } catch (e) {
            if (kDebugMode) {
              print('Warning: Could not set header $key: $e');
            }
          }
        });
      }

      // Set response type
      request.responseType = 'text';

      // Set timeout (optional)
      request.timeout = 30000; // 30 seconds

      // Handle successful response
      request.onLoad.listen((event) {
        try {
          if (request.status == 200) {
            final responseText = request.responseText;
            if (responseText != null && responseText.isNotEmpty) {
              final responseData = json.decode(responseText);
              completer.complete({
                'success': true,
                'data': responseData,
                'status': request.status,
                'headers': request.getAllResponseHeaders(),
              });
            } else {
              completer.complete({
                'success': false,
                'error': 'Empty response received',
                'status': request.status,
              });
            }
          } else {
            // Handle HTTP error status codes
            String errorMessage = 'Request failed';
            try {
              final errorResponse = request.responseText;
              if (errorResponse != null && errorResponse.isNotEmpty) {
                final errorData = json.decode(errorResponse);
                errorMessage =
                    errorData['error'] ?? errorData['message'] ?? errorMessage;
              }
            } catch (e) {
              // If error response is not JSON, use status text
              errorMessage = request.statusText ?? 'HTTP ${request.status}';
            }

            completer.complete({
              'success': false,
              'error': errorMessage,
              'status': request.status,
            });
          }
        } catch (e) {
          completer.complete({
            'success': false,
            'error': 'Failed to parse response: $e',
            'status': request.status,
          });
        }
      });

      // Handle network errors
      request.onError.listen((event) {
        completer.complete({
          'success': false,
          'error': 'Network error occurred. Please check your connection.',
          'status': request.status,
        });
      });

      // Handle timeout
      request.onTimeout.listen((event) {
        completer.complete({
          'success': false,
          'error': 'Request timeout. Please try again.',
          'status': request.status,
        });
      });

      // Send the request
      if (body != null) {
        request.send(json.encode(body));
      } else {
        request.send();
      }
    } catch (e) {
      completer.complete({
        'success': false,
        'error': 'Failed to create request: $e',
        'status': null,
      });
    }

    return completer.future;
  }

  /// Alternative implementation using Fetch API
  static Future<Map<String, dynamic>> makeUniversalFetchRequest({
    required String url,
    required String method,
    Map<String, String>? headers,
    dynamic body,
  }) async {
    try {
      final fetchOptions = <String, dynamic>{
        'method': method,
        'mode': 'cors',
        'credentials': 'omit',
      };

      if (headers != null) {
        fetchOptions['headers'] = headers;
      }

      if (body != null) {
        fetchOptions['body'] = json.encode(body);
      }

      final response = await html.window.fetch(url, fetchOptions);

      if (response.ok) {
        final textResponse = await response.text();
        final jsonData = json.decode(textResponse);
        return {
          'success': true,
          'data': jsonData,
          'status': response.status,
        };
      } else {
        String errorMessage = 'Request failed';
        try {
          final errorText = await response.text();
          if (errorText.isNotEmpty) {
            final errorData = json.decode(errorText);
            errorMessage =
                errorData['error'] ?? errorData['message'] ?? errorMessage;
          }
        } catch (e) {
          errorMessage = 'HTTP ${response.status}';
        }

        return {
          'success': false,
          'error': errorMessage,
          'status': response.status,
        };
      }
    } catch (e) {
      return {
        'success': false,
        'error': 'Network error: $e',
        'status': null,
      };
    }
  }
}
