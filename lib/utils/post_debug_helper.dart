import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../services/post_service.dart';

/// Helper class for debugging post feed issues
class PostDebugHelper {
  static final PostService _postService = PostService();

  /// Show debug information about current posts
  static void showPostDebugInfo(BuildContext context) {
    if (!kDebugMode) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Post Debug Info'),
        content: SizedBox(
          width: 400,
          height: 500,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('PostService Initialized: ${_postService.isInitialized}'),
                Text('Total Posts: ${_postService.totalPosts}'),
                Text('User Posts: ${_postService.userPostsCount}'),
                Text('Can Load More: ${_postService.canLoadMore}'),
                Text('Is Loading More: ${_postService.isLoadingMore}'),
                const SizedBox(height: 16),
                const Text('Recent Posts:', style: TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 8),
                ..._postService.getAllPosts().take(10).map((post) => 
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4),
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('ID: ${post.id}'),
                          Text('Author: ${post.author}'),
                          Text('Category: ${post.category}'),
                          Text('Price: ${post.formattedPrice}'),
                          Text('Created: ${post.timeAgo}'),
                          Text('Content: ${post.content.length > 50 ? post.content.substring(0, 50) + "..." : post.content}'),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          if (kDebugMode)
            TextButton(
              onPressed: () {
                _printPostDebugInfo();
                Navigator.of(context).pop();
              },
              child: const Text('Print to Console'),
            ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _refreshPosts(context);
            },
            child: const Text('Refresh Posts'),
          ),
        ],
      ),
    );
  }

  /// Print detailed debug information to console
  static void _printPostDebugInfo() {
    if (!kDebugMode) return;

    print('=== POST DEBUG INFO ===');
    print('PostService Initialized: ${_postService.isInitialized}');
    print('Total Posts: ${_postService.totalPosts}');
    print('User Posts: ${_postService.userPostsCount}');
    print('Can Load More: ${_postService.canLoadMore}');
    print('Is Loading More: ${_postService.isLoadingMore}');
    print('Average Post Price: \$${_postService.averagePostPrice.toStringAsFixed(2)}');
    print('Total Value in Posts: \$${_postService.totalValueInPosts.toStringAsFixed(2)}');
    
    print('\n=== RECENT POSTS ===');
    final posts = _postService.getAllPosts();
    for (int i = 0; i < posts.length && i < 20; i++) {
      final post = posts[i];
      print('${i + 1}. ${post.category} - ${post.formattedPrice}');
      print('   ID: ${post.id}');
      print('   Author: ${post.author}');
      print('   Created: ${post.createdAt}');
      print('   Content: ${post.content.length > 100 ? post.content.substring(0, 100) + "..." : post.content}');
      print('   ---');
    }
    
    print('\n=== POSTS BY CATEGORY ===');
    final postsByCategory = _postService.postsByCategory;
    postsByCategory.forEach((category, count) {
      print('$category: $count posts');
    });
    
    print('=== END POST DEBUG INFO ===');
  }

  /// Refresh posts and show result
  static Future<void> _refreshPosts(BuildContext context) async {
    try {
      await _postService.refreshPosts();
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Posts refreshed successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error refreshing posts: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Initialize PostService if not already initialized
  static Future<void> ensurePostServiceInitialized() async {
    if (!_postService.isInitialized) {
      await _postService.initialize();
      if (kDebugMode) {
        print('PostDebugHelper: PostService initialized');
      }
    }
  }

  /// Show posts by category breakdown
  static void showCategoryBreakdown(BuildContext context) {
    final postsByCategory = _postService.postsByCategory;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Posts by Category'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Total Posts: ${_postService.totalPosts}'),
            const SizedBox(height: 8),
            ...postsByCategory.entries.map((entry) => 
              Text('${entry.key}: ${entry.value} posts')
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
