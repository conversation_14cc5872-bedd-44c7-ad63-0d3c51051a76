import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../services/post_service.dart';

/// GetX Controller for managing posts state across the entire app
class PostController extends GetxController {
  static PostController get instance => Get.find();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final PostService _postService = PostService();

  // Reactive variables
  final _posts = <Post>[].obs;
  final _isLoading = false.obs;
  final _isInitialized = false.obs;
  final _selectedCategory = 'All'.obs;
  final _sortBy = 'Highest Paid'.obs;
  final _topPostCache = <String, Post?>{}.obs;
  final _lastTopPostUpdate = <String, DateTime>{}.obs;

  // Stream subscriptions
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? _postsSubscription;
  StreamController<List<Post>>? _postsController;

  // Pagination
  static const int _postsPerPage = 20;
  DocumentSnapshot? _lastDocument;
  bool _hasMorePosts = true;
  bool _isLoadingMore = false;

  // Cache duration for top posts (5 minutes)
  static const Duration _topPostCacheDuration = Duration(minutes: 5);

  // Getters
  List<Post> get posts => _posts.value;
  bool get isLoading => _isLoading.value;
  bool get isInitialized => _isInitialized.value;
  String get selectedCategory => _selectedCategory.value;
  String get sortBy => _sortBy.value;

  // Reactive getters for UI binding
  RxList<Post> get postsRx => _posts;
  RxBool get isLoadingRx => _isLoading;
  RxBool get isInitializedRx => _isInitialized;
  RxString get selectedCategoryRx => _selectedCategory;
  RxString get sortByRx => _sortBy;

  @override
  void onInit() {
    super.onInit();
    _initializeController();
  }

  @override
  void onClose() {
    _postsSubscription?.cancel();
    _postsController?.close();
    super.onClose();
  }

  /// Initialize the controller
  Future<void> _initializeController() async {
    if (_isInitialized.value) return;

    try {
      _isLoading.value = true;
      
      // Initialize PostService if needed
      if (!_postService.isInitialized) {
        await _postService.initialize();
      }

      // Set up real-time listener
      await _setupRealtimeListener();

      _isInitialized.value = true;
      debugPrint('PostController: Initialized successfully');
    } catch (e) {
      debugPrint('PostController: Error initializing: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// Set up real-time listener for posts
  Future<void> _setupRealtimeListener() async {
    _postsSubscription?.cancel();

    _postsSubscription = _firestore
        .collection('posts')
        .orderBy('createdAt', descending: true)
        .limit(_postsPerPage)
        .snapshots()
        .listen(
      (snapshot) {
        _updatePostsFromSnapshot(snapshot);
        _invalidateTopPostCache();
        debugPrint('PostController: Real-time update - ${_posts.length} posts');
      },
      onError: (error) {
        debugPrint('PostController: Error in real-time listener: $error');
      },
    );
  }

  /// Update posts from Firestore snapshot
  void _updatePostsFromSnapshot(QuerySnapshot<Map<String, dynamic>> snapshot) {
    final newPosts = snapshot.docs.map((doc) {
      final data = doc.data();
      final map = Map<String, dynamic>.from(data)..['id'] = doc.id;
      return Post.fromMap(map);
    }).toList();

    // Update posts list
    _posts.value = newPosts;

    // Update pagination state
    if (snapshot.docs.isNotEmpty) {
      _lastDocument = snapshot.docs.last;
      _hasMorePosts = snapshot.docs.length == _postsPerPage;
    } else {
      _hasMorePosts = false;
    }

    // Sort posts based on current sort criteria
    _applySorting();
  }

  /// Apply sorting to posts
  void _applySorting() {
    final sortedPosts = List<Post>.from(_posts.value);
    
    switch (_sortBy.value) {
      case 'Highest Paid':
        sortedPosts.sort((a, b) {
          final priceComparison = b.price.compareTo(a.price);
          if (priceComparison != 0) return priceComparison;
          return b.createdAt.compareTo(a.createdAt);
        });
        break;
      case 'Most Popular':
        sortedPosts.sort((a, b) {
          final likesComparison = b.likes.compareTo(a.likes);
          if (likesComparison != 0) return likesComparison;
          return b.createdAt.compareTo(a.createdAt);
        });
        break;
      case 'Recent':
        sortedPosts.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case 'Most Views':
        sortedPosts.sort((a, b) {
          final viewsComparison = b.views.compareTo(a.views);
          if (viewsComparison != 0) return viewsComparison;
          return b.createdAt.compareTo(a.createdAt);
        });
        break;
    }

    _posts.value = sortedPosts;
  }

  /// Get filtered and sorted posts
  List<Post> getFilteredPosts({String? category}) {
    final categoryFilter = category ?? _selectedCategory.value;
    
    if (categoryFilter == 'All') {
      return List<Post>.from(_posts.value);
    }
    
    return _posts.value.where((post) => post.category == categoryFilter).toList();
  }

  /// Get filtered posts as a reactive stream
  Stream<List<Post>> getFilteredPostsStream({String? category}) {
    return _posts.stream.map((posts) {
      final categoryFilter = category ?? _selectedCategory.value;
      
      if (categoryFilter == 'All') {
        return List<Post>.from(posts);
      }
      
      return posts.where((post) => post.category == categoryFilter).toList();
    });
  }

  /// Update category filter
  void updateCategory(String category) {
    if (_selectedCategory.value != category) {
      _selectedCategory.value = category;
      debugPrint('PostController: Updated category to $category');
    }
  }

  /// Update sort criteria
  void updateSortBy(String sortBy) {
    if (_sortBy.value != sortBy) {
      _sortBy.value = sortBy;
      _applySorting();
      debugPrint('PostController: Updated sort to $sortBy');
    }
  }

  /// Get top paid post for a category with caching
  Post? getTopPaidPostForCategory(String category) {
    final now = DateTime.now();
    final cacheKey = category;
    
    // Check if we have a valid cached result
    if (_topPostCache.containsKey(cacheKey) && 
        _lastTopPostUpdate.containsKey(cacheKey)) {
      final lastUpdate = _lastTopPostUpdate[cacheKey]!;
      if (now.difference(lastUpdate) < _topPostCacheDuration) {
        return _topPostCache[cacheKey];
      }
    }

    // Calculate new top post
    final filteredPosts = getFilteredPosts(category: category);
    final paidPosts = filteredPosts.where((post) => post.price > 0).toList();
    
    if (paidPosts.isEmpty) {
      _topPostCache[cacheKey] = null;
      _lastTopPostUpdate[cacheKey] = now;
      return null;
    }

    // Get posts from last 24 hours
    final last24Hours = paidPosts.where((post) {
      return now.difference(post.createdAt).inHours <= 24;
    }).toList();

    final postsToSort = last24Hours.isNotEmpty ? last24Hours : paidPosts;
    
    // Sort by highest price first, then by latest creation date
    postsToSort.sort((a, b) {
      final priceComparison = b.price.compareTo(a.price);
      if (priceComparison != 0) return priceComparison;
      return b.createdAt.compareTo(a.createdAt);
    });

    final topPost = postsToSort.isNotEmpty ? postsToSort.first : null;
    
    // Cache the result
    _topPostCache[cacheKey] = topPost;
    _lastTopPostUpdate[cacheKey] = now;
    
    return topPost;
  }

  /// Invalidate top post cache
  void _invalidateTopPostCache() {
    _topPostCache.clear();
    _lastTopPostUpdate.clear();
  }

  /// Force refresh posts
  Future<void> refreshPosts() async {
    try {
      _isLoading.value = true;
      
      // Reset pagination
      _lastDocument = null;
      _hasMorePosts = true;
      _isLoadingMore = false;
      
      // Invalidate cache
      _invalidateTopPostCache();
      
      // Restart listener
      await _setupRealtimeListener();
      
      debugPrint('PostController: Posts refreshed');
    } catch (e) {
      debugPrint('PostController: Error refreshing posts: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// Create a new post
  Future<String> createPost({
    String? title,
    required String content,
    required double price,
    required String category,
    List<String>? tags,
    bool isPublic = true,
    bool allowComments = true,
    String? imageUrl,
    List<String>? imageUrls,
    List<String>? videoUrls,
    String? linkUrl,
  }) async {
    final postId = await _postService.createPost(
      title: title,
      content: content,
      price: price,
      category: category,
      tags: tags,
      isPublic: isPublic,
      allowComments: allowComments,
      imageUrl: imageUrl,
      imageUrls: imageUrls,
      videoUrls: videoUrls,
      linkUrl: linkUrl,
    );

    // Invalidate cache since we have a new post
    _invalidateTopPostCache();
    
    return postId;
  }

  /// Get categories
  List<String> getCategories() {
    return _postService.getCategories();
  }

  /// Get sort options
  List<String> getSortOptions() {
    return _postService.getSortOptions();
  }
}
