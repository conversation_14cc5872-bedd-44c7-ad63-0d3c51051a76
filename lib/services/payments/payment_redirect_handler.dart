import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:web/web.dart' as web;

class PaymentRedirectHandler {
  static const String _baseUrl = 'https://us-central1-money-mouthy.cloudfunctions.net';

  /// Check if the current URL contains payment redirect parameters
  static bool hasPaymentRedirect() {
    if (!kIsWeb) return false;
    
    final url = web.window.location.href;
    return url.contains('payment_intent=') || url.contains('payment_modal=true');
  }

  /// Extract payment intent ID from URL parameters
  static String? getPaymentIntentFromUrl() {
    if (!kIsWeb) return null;
    
    final url = web.window.location.href;
    final uri = Uri.parse(url);
    
    return uri.queryParameters['payment_intent'];
  }

  /// Get redirect status from URL
  static String? getRedirectStatus() {
    if (!kIsWeb) return null;
    
    final url = web.window.location.href;
    final uri = Uri.parse(url);
    
    return uri.queryParameters['redirect_status'];
  }

  /// Check if this is a payment modal redirect
  static bool isPaymentModalRedirect() {
    if (!kIsWeb) return false;
    
    final url = web.window.location.href;
    final uri = Uri.parse(url);
    
    return uri.queryParameters['payment_modal'] == 'true';
  }

  /// Handle payment redirect and process the result
  static Future<PaymentRedirectResult> handlePaymentRedirect(BuildContext context) async {
    try {
      final paymentIntentId = getPaymentIntentFromUrl();
      final redirectStatus = getRedirectStatus();
      
      if (paymentIntentId == null) {
        return PaymentRedirectResult.error('No payment intent found in URL');
      }

      if (redirectStatus == 'succeeded') {
        // Process successful payment
        final success = await _processPaymentCompletion(paymentIntentId);
        if (success) {
          _cleanUrl();
          return PaymentRedirectResult.success('Payment completed successfully');
        } else {
          return PaymentRedirectResult.error('Failed to process payment completion');
        }
      } else if (redirectStatus == 'failed') {
        _cleanUrl();
        return PaymentRedirectResult.error('Payment failed');
      } else if (redirectStatus == 'canceled') {
        _cleanUrl();
        return PaymentRedirectResult.cancelled('Payment was cancelled');
      } else {
        _cleanUrl();
        return PaymentRedirectResult.error('Unknown payment status: $redirectStatus');
      }
    } catch (e) {
      _cleanUrl();
      return PaymentRedirectResult.error('Error handling payment redirect: $e');
    }
  }

  /// Process payment completion by calling our Firebase Function
  static Future<bool> _processPaymentCompletion(String paymentIntentId) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/handlePaymentCompletion'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'paymentIntentId': paymentIntentId,
          'userId': user.uid,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['success'] == true;
      } else {
        if (kDebugMode) {
          print('Payment completion failed: ${response.statusCode} - ${response.body}');
        }
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error processing payment completion: $e');
      }
      return false;
    }
  }

  /// Clean the URL by removing payment-related query parameters
  static void _cleanUrl() {
    if (!kIsWeb) return;
    
    try {
      final currentUrl = web.window.location.href;
      final uri = Uri.parse(currentUrl);
      
      // Remove payment-related parameters
      final cleanParams = Map<String, String>.from(uri.queryParameters);
      cleanParams.remove('payment_intent');
      cleanParams.remove('payment_intent_client_secret');
      cleanParams.remove('redirect_status');
      cleanParams.remove('payment_modal');
      
      // Build clean URL
      final cleanUri = uri.replace(queryParameters: cleanParams.isEmpty ? null : cleanParams);
      
      // Update browser URL without reload
      web.window.history.replaceState(null, '', cleanUri.toString());
    } catch (e) {
      if (kDebugMode) {
        print('Error cleaning URL: $e');
      }
    }
  }

  /// Show appropriate message based on payment result
  static void showPaymentResult(BuildContext context, PaymentRedirectResult result) {
    Color backgroundColor;
    IconData icon;
    
    switch (result.type) {
      case PaymentRedirectType.success:
        backgroundColor = Colors.green;
        icon = Icons.check_circle;
        break;
      case PaymentRedirectType.error:
        backgroundColor = Colors.red;
        icon = Icons.error;
        break;
      case PaymentRedirectType.cancelled:
        backgroundColor = Colors.orange;
        icon = Icons.cancel;
        break;
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(icon, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(child: Text(result.message)),
          ],
        ),
        backgroundColor: backgroundColor,
        duration: const Duration(seconds: 4),
      ),
    );
  }
}

/// Result of payment redirect handling
class PaymentRedirectResult {
  final PaymentRedirectType type;
  final String message;

  const PaymentRedirectResult._(this.type, this.message);

  factory PaymentRedirectResult.success(String message) =>
      PaymentRedirectResult._(PaymentRedirectType.success, message);

  factory PaymentRedirectResult.error(String message) =>
      PaymentRedirectResult._(PaymentRedirectType.error, message);

  factory PaymentRedirectResult.cancelled(String message) =>
      PaymentRedirectResult._(PaymentRedirectType.cancelled, message);

  bool get isSuccess => type == PaymentRedirectType.success;
  bool get isError => type == PaymentRedirectType.error;
  bool get isCancelled => type == PaymentRedirectType.cancelled;
}

enum PaymentRedirectType {
  success,
  error,
  cancelled,
}
