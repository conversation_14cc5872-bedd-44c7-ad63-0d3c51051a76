import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_stripe_web/flutter_stripe_web.dart';

Future<void> pay(BuildContext context) async {
  try {
    final result = await WebStripe.instance.confirmPaymentElement(
      ConfirmPaymentElementOptions(
        confirmParams:
            ConfirmPaymentParams(return_url: "https://www.google.com"),
      ),
    );
    print(" Results ------------------- $result");
    // // This part will only execute if the payment doesn't redirect
    // if (result.status == PaymentIntentsStatus.Succeeded) {
    //   Navigator.push(
    //     context,
    //     MaterialPageRoute(
    //       builder: (context) => PaymentResultScreen(
    //         success: true,
    //         message:
    //             'Your payment was processed successfully. Transaction ID: ${result.id}',
    //       ),
    //     ),
    //   );
    // } else if (result.status == PaymentIntentsStatus.Processing) {
    //   Navigator.push(
    //     context,
    //     MaterialPageRoute(
    //       builder: (context) => PaymentResultScreen(
    //         success: true,
    //         message:
    //             'Your payment is being processed. We\'ll update you once completed.',
    //       ),
    //     ),
    //   );
    // } else {
    //   Navigator.push(
    //     context,
    //     MaterialPageRoute(
    //       builder: (context) => PaymentResultScreen(
    //         success: false,
    //         message:
    //             'Payment status: ${result.status}. Please try again or contact support.',
    //       ),
    //     ),
    //   );
    // }
  } catch (e) {
    // Most payment methods will redirect, so we don't need to handle errors here
    // The redirect handler will take care of showing the result
    print('Payment process: ${e.toString()}');
  }
}

class PaymentScreen extends StatefulWidget {
  const PaymentScreen({super.key, this.clientSecret, this.amount, this.onPay});

  final String? clientSecret;
  final double? amount;
  final VoidCallback? onPay;

  @override
  State<PaymentScreen> createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> {
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          border: Border.all(
        color: Colors.red,
      )),
      child: Column(
        children: [
          PaymentElement(
            autofocus: true,
            enablePostalCode: true,
            onCardChanged: (_) {},
            clientSecret: widget.clientSecret ?? '',
          ),
          ElevatedButton(onPressed: () => pay(context), child: Text('Pay'))
        ],
      ),
    );
  }
}
