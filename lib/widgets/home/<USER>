import 'package:flutter/material.dart';
import 'package:money_mouthy_two/services/post_service.dart';
import 'package:money_mouthy_two/widgets/top_paid_post_container.dart';
import 'package:money_mouthy_two/screens/post_detail_screen.dart';
import 'package:money_mouthy_two/utils/debouncer.dart';
import 'posts_feed.dart';

/// Explore Tab Widget
class ExploreTab extends StatefulWidget {
  final String selectedCategory;

  const ExploreTab({super.key, required this.selectedCategory});

  @override
  State<ExploreTab> createState() => _ExploreTabState();
}

class _ExploreTabState extends State<ExploreTab> with DebounceMixin {
  final PostService _postService = PostService();
  Post? _cachedTopPost;
  String? _lastCategory;

  @override
  void initState() {
    super.initState();
    // Initialize PostService if needed
    if (!_postService.isInitialized) {
      _postService.initialize();
    }
  }

  @override
  void dispose() {
    disposeAllDebouncers();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Category Header
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Row(
            children: [
              // Icon(Icons.category, color: Colors.grey[600], size: 20),
              // const SizedBox(width: 8),
              Text(
                widget.selectedCategory,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
        ),
        // Top Paid Post Container (24-hour system) - Optimized with caching
        StreamBuilder<List<Post>>(
          stream: _postService.postsStream,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting &&
                _cachedTopPost == null) {
              return Container(
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                height: 120,
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: const Center(child: CircularProgressIndicator()),
              );
            }

            if (snapshot.hasError ||
                (!snapshot.hasData && _cachedTopPost == null)) {
              return const SizedBox.shrink();
            }

            // Only recalculate if category changed or we have new data
            if (_lastCategory != widget.selectedCategory ||
                (snapshot.hasData && snapshot.data!.isNotEmpty)) {
              _cachedTopPost = _postService.getTopPaidPostForCategory(
                widget.selectedCategory,
              );
              _lastCategory = widget.selectedCategory;
            }

            if (_cachedTopPost == null) {
              return const SizedBox.shrink();
            }

            return LayoutBuilder(
              builder: (context, constraints) {
                return TopPaidPostContainer(
                  category: widget.selectedCategory,
                  topPost: _cachedTopPost!,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder:
                            (context) =>
                                PostDetailScreen(post: _cachedTopPost!),
                      ),
                    );
                  },
                );
              },
            );
          },
        ),
        Expanded(
          child: PostsFeed(
            key: ValueKey(widget.selectedCategory),
            category: widget.selectedCategory,
          ),
        ),
      ],
    );
  }
}
