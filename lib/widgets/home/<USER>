import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:money_mouthy_two/controllers/wallet_controller.dart';
import 'package:money_mouthy_two/controllers/post_controller.dart';
import 'package:money_mouthy_two/services/post_service.dart';
import 'package:money_mouthy_two/widgets/post_card.dart';
import 'package:money_mouthy_two/widgets/skeleton_loader.dart';
import 'package:money_mouthy_two/screens/post_detail_screen.dart';

/// Posts Feed Widget - Now using PostController for reactive state management
class PostsFeed extends StatefulWidget {
  final String category;

  const PostsFeed({super.key, required this.category});

  @override
  State<PostsFeed> createState() => _PostsFeedState();
}

class _PostsFeedState extends State<PostsFeed> {
  final PostController _postController = Get.find<PostController>();
  final WalletController walletController = Get.find<WalletController>();
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _setupScrollListener();
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent - 200) {
        _loadMorePosts();
      }
    });
  }

  Future<void> _loadMorePosts() async {
    // For now, we'll disable pagination since PostController handles initial loading
    // This can be enhanced later if needed
    return;
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('PostsFeed: Building widget for category ${widget.category}');

    return Obx(() {
      if (_postController.isLoading) {
        debugPrint('PostsFeed: Showing skeleton loader');
        return ListView.builder(
          padding: const EdgeInsets.fromLTRB(12, 4, 12, 80),
          itemCount: 5,
          itemBuilder: (context, index) => const PostCardSkeleton(),
        );
      }

      final allPosts =
          _postController.getFilteredPosts(category: widget.category);

      // Get the top paid post for this category to exclude it from regular feed
      final topPost =
          _postController.getTopPaidPostForCategory(widget.category);

      final posts = List<Post>.from(allPosts);

      // Remove the top paid post from regular feed to avoid duplication
      if (topPost != null) {
        posts.removeWhere((post) => post.id == topPost.id);
      }

      debugPrint(
        'PostsFeed: Displaying ${posts.length} posts for category ${widget.category}',
      );

      if (posts.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.post_add, size: 64, color: Colors.grey[400]),
              const SizedBox(height: 16),
              Text(
                'No posts in ${widget.category}',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Be the first to share something!',
                style: TextStyle(fontSize: 14, color: Colors.grey[500]),
              ),
            ],
          ),
        );
      }

      return RefreshIndicator(
        onRefresh: () async {
          HapticFeedback.mediumImpact();
          await _postController.refreshPosts();
        },
        color: const Color(0xFF4285F4),
        backgroundColor: Colors.white,
        strokeWidth: 3,
        displacement: 60,
        child: ListView.builder(
          controller: _scrollController,
          physics: const BouncingScrollPhysics(
            parent: AlwaysScrollableScrollPhysics(),
          ),
          padding: const EdgeInsets.fromLTRB(12, 4, 12, 80),
          itemCount: posts.length,
          itemBuilder: (context, index) {
            return PostCard(
              post: posts[index],
              onLike: () => _handleLike(posts[index]),
              onPurchase: () => _handlePurchase(posts[index]),
              onView: () => _handleView(posts[index]),
              onTap: () => _handlePostTap(posts[index]),
            );
          },
        ),
      );
    });
  }

  void _handleLike(Post post) {
    // Use PostService for individual post actions
    PostService().likePost(post.id);
  }

  Future<void> _handlePurchase(Post post) async {
    // Check wallet balance
    final currentBalance = walletController.balance;
    if (post.price > currentBalance) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Insufficient funds. Your balance: ${walletController.formatCurrency(currentBalance)}',
          ),
          backgroundColor: Colors.red,
          action: SnackBarAction(
            label: 'Add Funds',
            onPressed: () {
              Navigator.pushNamed(context, '/wallet');
            },
          ),
        ),
      );
      return;
    }

    // Deduct balance for post purchase
    final balanceDeducted = await walletController.deductFunds(
      amount: post.price,
      description:
          'Purchased post: ${post.title ?? post.content.substring(0, 30)}...',
      postId: post.id,
    );

    if (balanceDeducted) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Post purchased for ${post.formattedPrice}!'),
            backgroundColor: Colors.green,
          ),
        );
      }
      // TODO: Mark post as purchased for user
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Purchase failed. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _handleView(Post post) {
    PostService().viewPost(post.id);
  }

  void _handlePostTap(Post post) {
    // Navigate to post detail view
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => PostDetailScreen(post: post)),
    );
  }
}
